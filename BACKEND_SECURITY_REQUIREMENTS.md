# 🔒 Backend Security Requirements for Order Checkout

## 🚨 CRITICAL SECURITY IMPLEMENTATION

The web OrderCheckout page has been hardened against client-side manipulation. The backend MUST implement the following security measures:

## 1. 🛡️ ORDER CREATION ENDPOINT SECURITY

### Endpoint: `POST /api/orders`

**NEVER TRUST CLIENT-SENT PRICES** - The backend must:

```typescript
// ❌ NEVER DO THIS - Trusting client prices
const order = {
  subtotal: req.body.subtotal, // VULNERABLE!
  deliveryFee: req.body.deliveryFee, // VULNERABLE!
  totalAmount: req.body.totalAmount // VULNERABLE!
}

// ✅ ALWAYS DO THIS - Recalculate everything
const order = await calculateSecureOrder({
  supplierId: req.body.supplierId,
  items: req.body.items, // Only IDs and quantities
  deliveryAddress: req.body.deliveryAddress,
  promoCode: req.body.promoCode
});
```

### Required Validation Steps:

1. **Product Price Lookup**
   ```sql
   SELECT price FROM products WHERE id = ? AND supplier_id = ?
   ```

2. **Addon/Option Validation**
   ```sql
   SELECT price FROM product_addons WHERE id = ? AND product_id = ?
   ```

3. **Delivery Fee Calculation**
   ```typescript
   const deliveryFee = calculateDeliveryFee(supplierId, deliveryAddress);
   ```

4. **Promo Code Validation**
   ```typescript
   const discount = await validateAndCalculatePromoDiscount(promoCode, subtotal);
   ```

5. **Final Total Calculation**
   ```typescript
   const finalTotal = subtotal + deliveryFee - discount;
   ```

## 2. 🔐 REQUIRED DATABASE SCHEMA

### Products Table
```sql
CREATE TABLE products (
  id VARCHAR(255) PRIMARY KEY,
  supplier_id VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  base_price DECIMAL(10,2) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
);
```

### Product Addons Table
```sql
CREATE TABLE product_addons (
  id VARCHAR(255) PRIMARY KEY,
  product_id VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  FOREIGN KEY (product_id) REFERENCES products(id)
);
```

### Promo Codes Table
```sql
CREATE TABLE promo_codes (
  code VARCHAR(50) PRIMARY KEY,
  discount_type ENUM('fixed', 'percentage') NOT NULL,
  discount_amount DECIMAL(10,2) NOT NULL,
  minimum_order_amount DECIMAL(10,2) DEFAULT 0,
  max_uses INT DEFAULT NULL,
  current_uses INT DEFAULT 0,
  valid_from TIMESTAMP NOT NULL,
  valid_to TIMESTAMP NOT NULL,
  is_active BOOLEAN DEFAULT true
);
```

### Orders Table
```sql
CREATE TABLE orders (
  order_id VARCHAR(255) PRIMARY KEY,
  customer_id VARCHAR(255) NOT NULL,
  supplier_id VARCHAR(255) NOT NULL,
  supplier_name VARCHAR(255) NOT NULL,
  
  -- CALCULATED BY BACKEND ONLY
  subtotal DECIMAL(10,2) NOT NULL,
  delivery_fee DECIMAL(10,2) NOT NULL,
  promo_discount DECIMAL(10,2) DEFAULT 0,
  total_amount DECIMAL(10,2) NOT NULL,
  
  payment_method ENUM('cash', 'card', 'wallet') NOT NULL,
  delivery_address JSON NOT NULL,
  customer_phone VARCHAR(20) NOT NULL,
  notes TEXT,
  promo_code VARCHAR(50),
  
  status ENUM('pending', 'confirmed', 'preparing', 'ready', 'picked_up', 'on_the_way', 'delivered', 'cancelled') DEFAULT 'pending',
  estimated_delivery_time VARCHAR(50),
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (customer_id) REFERENCES users(id),
  FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
  FOREIGN KEY (promo_code) REFERENCES promo_codes(code)
);
```

## 3. 🔍 SECURITY VALIDATION ALGORITHM

```typescript
async function createSecureOrder(orderRequest) {
  // 1. Validate user authentication
  const user = await validateAuthToken(req.headers.authorization);
  if (!user) throw new Error('Unauthorized');

  // 2. Validate supplier exists and is active
  const supplier = await getActiveSupplier(orderRequest.supplierId);
  if (!supplier) throw new Error('Invalid supplier');

  let calculatedSubtotal = 0;

  // 3. Validate each order item and calculate prices
  for (const item of orderRequest.items) {
    // Get product from database
    const product = await getProduct(item.productId, orderRequest.supplierId);
    if (!product || !product.is_active) {
      throw new Error(`Invalid product: ${item.productId}`);
    }

    // Calculate item price with addons
    let itemPrice = product.base_price;
    
    // Validate and add addon prices
    if (item.options?.selectedAdditions) {
      for (const addon of item.options.selectedAdditions) {
        const validAddon = await getProductAddon(addon.id, item.productId);
        if (!validAddon) throw new Error(`Invalid addon: ${addon.id}`);
        itemPrice += validAddon.price;
      }
    }

    calculatedSubtotal += itemPrice * item.quantity;
  }

  // 4. Calculate delivery fee
  const deliveryFee = await calculateDeliveryFee(
    orderRequest.supplierId,
    orderRequest.deliveryAddress
  );

  // 5. Validate and calculate promo discount
  let promoDiscount = 0;
  if (orderRequest.promoCode) {
    promoDiscount = await validatePromoCode(
      orderRequest.promoCode,
      calculatedSubtotal
    );
  }

  // 6. Calculate final total
  const finalTotal = calculatedSubtotal + deliveryFee - promoDiscount;

  // 7. Create order with calculated values
  const order = await createOrder({
    customerId: user.id,
    supplierId: orderRequest.supplierId,
    items: orderRequest.items,
    subtotal: calculatedSubtotal,
    deliveryFee: deliveryFee,
    promoDiscount: promoDiscount,
    totalAmount: finalTotal,
    // ... other fields
  });

  return order;
}
```

## 4. 🚫 SECURITY VIOLATIONS TO PREVENT

### ❌ Price Manipulation
- Client sends fake `finalPrice` values
- Client modifies cart data in localStorage
- Client sends inflated addon prices

### ❌ Delivery Fee Bypass
- Client sends `deliveryFee: 0`
- Client manipulates supplier count

### ❌ Promo Code Abuse
- Client sends fake discount amounts
- Client bypasses usage limits
- Client uses expired codes

### ❌ Product Validation Bypass
- Client sends non-existent product IDs
- Client orders from wrong supplier
- Client orders inactive products

## 5. ✅ IMPLEMENTATION CHECKLIST

- [ ] Product price lookup from database
- [ ] Addon price validation and calculation
- [ ] Delivery fee calculation per supplier
- [ ] Promo code validation with usage tracking
- [ ] Order total recalculation
- [ ] Input sanitization and validation
- [ ] Rate limiting on order creation
- [ ] Audit logging for price discrepancies
- [ ] Error handling without information leakage

## 6. 🔄 FRONTEND-BACKEND CONTRACT

**Frontend Sends:**
- Product IDs and quantities only
- Selected options (IDs, not prices)
- Delivery address
- Payment method
- Promo code

**Backend Returns:**
- Calculated subtotal
- Calculated delivery fee
- Applied discount amount
- Final total amount
- Order confirmation

**Frontend displays backend-calculated values in confirmation.**
