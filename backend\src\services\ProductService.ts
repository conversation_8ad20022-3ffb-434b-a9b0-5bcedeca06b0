/**
 * 🔒 SECURE PRODUCT SERVICE
 * 
 * This service handles product price validation and calculation
 * to prevent client-side price manipulation
 */
import { Supplier, Product } from '../models/Supplier';

export interface ProductValidationResult {
  isValid: boolean;
  product?: any;
  calculatedPrice: number;
  error?: string;
}

export interface OrderItemCalculation {
  productId: string;
  basePrice: number;
  addonsPrice: number;
  finalPrice: number;
  quantity: number;
  subtotal: number;
}

export class ProductService {
  /**
   * Validate product exists and belongs to supplier
   */
  static async validateProduct(productId: string, supplierId: string): Promise<ProductValidationResult> {
    try {
      const product = await Product.findOne({ 
        _id: productId, 
        supplierId: supplierId,
        isActive: true 
      });

      if (!product) {
        return {
          isValid: false,
          calculatedPrice: 0,
          error: `Product ${productId} not found or inactive for supplier ${supplierId}`
        };
      }

      return {
        isValid: true,
        product,
        calculatedPrice: product.price
      };
    } catch (error) {
      return {
        isValid: false,
        calculatedPrice: 0,
        error: `Error validating product: ${error}`
      };
    }
  }

  /**
   * Calculate secure price for order item including addons
   */
  static async calculateItemPrice(
    productId: string, 
    supplierId: string, 
    quantity: number,
    options?: any
  ): Promise<OrderItemCalculation> {
    // Validate base product
    const productValidation = await this.validateProduct(productId, supplierId);
    
    if (!productValidation.isValid) {
      throw new Error(productValidation.error || 'Invalid product');
    }

    let basePrice = productValidation.calculatedPrice;
    let addonsPrice = 0;

    // Calculate addon prices securely
    if (options?.selectedAdditions) {
      for (const addon of options.selectedAdditions) {
        const addonPrice = await this.validateAddonPrice(productId, addon.id);
        addonsPrice += addonPrice;
      }
    }

    if (options?.selectedSides) {
      for (const side of options.selectedSides) {
        const sidePrice = await this.validateAddonPrice(productId, side.id);
        addonsPrice += sidePrice;
      }
    }

    const finalPrice = basePrice + addonsPrice;
    const subtotal = finalPrice * quantity;

    return {
      productId,
      basePrice,
      addonsPrice,
      finalPrice,
      quantity,
      subtotal
    };
  }

  /**
   * Validate addon/side price from database
   */
  private static async validateAddonPrice(productId: string, addonId: string): Promise<number> {
    try {
      // This would query your product addons table
      // For now, returning 0 as placeholder - implement based on your schema
      
      // Example implementation:
      // const addon = await ProductAddon.findOne({ 
      //   _id: addonId, 
      //   productId: productId,
      //   isActive: true 
      // });
      // return addon ? addon.price : 0;
      
      return 0; // Placeholder - implement based on your addon schema
    } catch (error) {
      console.error(`Error validating addon ${addonId}:`, error);
      return 0;
    }
  }

  /**
   * Calculate total for multiple items securely
   */
  static async calculateOrderSubtotal(
    items: Array<{
      productId: string;
      quantity: number;
      options?: any;
    }>,
    supplierId: string
  ): Promise<{ subtotal: number; itemCalculations: OrderItemCalculation[] }> {
    const itemCalculations: OrderItemCalculation[] = [];
    let subtotal = 0;

    for (const item of items) {
      const calculation = await this.calculateItemPrice(
        item.productId,
        supplierId,
        item.quantity,
        item.options
      );
      
      itemCalculations.push(calculation);
      subtotal += calculation.subtotal;
    }

    return { subtotal, itemCalculations };
  }
}
