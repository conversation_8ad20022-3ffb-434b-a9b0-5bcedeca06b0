/**
 * 🔒 SECURE PRODUCT SERVICE
 * 
 * This service handles product price validation and calculation
 * to prevent client-side price manipulation
 */
import { Supplier, Product } from '../models/Supplier';

export interface ProductValidationResult {
  isValid: boolean;
  product?: any;
  calculatedPrice: number;
  error?: string;
}

export interface OrderItemCalculation {
  productId: string;
  basePrice: number;
  addonsPrice: number;
  finalPrice: number;
  quantity: number;
  subtotal: number;
}

export class ProductService {
  /**
   * Validate product exists and belongs to supplier
   */
  static async validateProduct(productId: string, supplierId: string): Promise<ProductValidationResult> {
    try {
      // Find supplier first (products are embedded in supplier document)
      let supplier;

      // Try to find supplier by _id first (ObjectId)
      if (supplierId.length === 24 && /^[0-9a-fA-F]{24}$/.test(supplierId)) {
        supplier = await Supplier.findOne({
          _id: supplierId,
          isActive: true
        });
      }

      // If not found, try to find by id field (string)
      if (!supplier) {
        supplier = await Supplier.findOne({
          id: supplierId,
          isActive: true
        });
      }

      if (!supplier) {
        return {
          isValid: false,
          calculatedPrice: 0,
          error: `Supplier ${supplierId} not found or inactive`
        };
      }

      // Find product within supplier's products array
      const product = supplier.products.find((p: any) => p.id === productId);

      if (!product || !product.isAvailable) {
        return {
          isValid: false,
          calculatedPrice: 0,
          error: `Product ${productId} not found or unavailable for supplier ${supplierId}`
        };
      }

      let finalPrice;
      if (!product.discountPrice || product.discountPrice == 0) {
        finalPrice = product.price;
      } else {
        finalPrice = product.discountPrice;
      }

      return {
        isValid: true,
        product,
        calculatedPrice: finalPrice
      };
    } catch (error) {
      return {
        isValid: false,
        calculatedPrice: 0,
        error: `Error validating product: ${error}`
      };
    }
  }

  /**
   * Calculate secure price for order item including addons
   */
  static async calculateItemPrice(
    productId: string, 
    supplierId: string, 
    quantity: number,
    options?: any
  ): Promise<OrderItemCalculation> {
    // Validate base product
    const productValidation = await this.validateProduct(productId, supplierId);
    
    if (!productValidation.isValid) {
      throw new Error(productValidation.error || 'Invalid product');
    }

    let basePrice = productValidation.calculatedPrice;
    let addonsPrice = 0;

    // Calculate addon prices securely
    if (options?.selectedAdditions) {
      for (const addon of options.selectedAdditions) {
        const addonPrice = await this.validateAddonPrice(productId, addon.id, supplierId, 'additions');
        addonsPrice += addonPrice;
      }
    }

    if (options?.selectedSides) {
      for (const side of options.selectedSides) {
        const sidePrice = await this.validateAddonPrice(productId, side.id, supplierId, 'sides');
        addonsPrice += sidePrice;
      }
    }

    const finalPrice = basePrice + addonsPrice;
    const subtotal = finalPrice * quantity;

    return {
      productId,
      basePrice,
      addonsPrice,
      finalPrice,
      quantity,
      subtotal
    };
  }

  /**
   * Validate addon/side price from database
   */
  private static async validateAddonPrice(
    productId: string,
    addonId: string,
    supplierId: string,
    addonType: 'additions' | 'sides' = 'additions'
  ): Promise<number> {
    try {
      // Find supplier and product
      let supplier;

      // Try to find supplier by _id first (ObjectId)
      if (supplierId.length === 24 && /^[0-9a-fA-F]{24}$/.test(supplierId)) {
        supplier = await Supplier.findOne({
          _id: supplierId,
          isActive: true
        });
      }

      // If not found, try to find by id field (string)
      if (!supplier) {
        supplier = await Supplier.findOne({
          id: supplierId,
          isActive: true
        });
      }

      if (!supplier) {
        return 0;
      }

      // Find product within supplier's products array
      const product = supplier.products.find((p: any) => p.id === productId);

      if (!product || !product.restaurantOptions) {
        return 0;
      }

      // Find addon in the appropriate array
      const addons = product.restaurantOptions[addonType] || [];
      const addon = addons.find((a: any) => a.id === addonId);

      return addon ? addon.price : 0;
    } catch (error) {
      console.error(`Error validating addon ${addonId}:`, error);
      return 0;
    }
  }

  /**
   * Calculate total for multiple items securely
   */
  static async calculateOrderSubtotal(
    items: Array<{
      productId: string;
      quantity: number;
      options?: any;
    }>,
    supplierId: string
  ): Promise<{ subtotal: number; itemCalculations: OrderItemCalculation[] }> {
    const itemCalculations: OrderItemCalculation[] = [];
    let subtotal = 0;

    for (const item of items) {
      const calculation = await this.calculateItemPrice(
        item.productId,
        supplierId,
        item.quantity,
        item.options
      );
      
      itemCalculations.push(calculation);
      subtotal += calculation.subtotal;
    }

    return { subtotal, itemCalculations };
  }
}
