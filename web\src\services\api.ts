// Web API Service - mirrors the mobile API service
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
}

export interface User {
  id: string;
  email: string;
  role: 'customer' | 'supplier';

  // Basic Information
  firstName: string;
  lastName: string;
  username: string;
  phoneNumber: string;

  // Profile Information
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';

  // Address Information
  address?: string;
  city?: string;
  country?: string;

  // Business Information (for suppliers)
  // supplierId?: string;
  // storeName?: string;
  // businessType?: 'restaurant' | 'clothing' | 'grocery' | 'pharmacy' | 'electronics' | 'other';
  // openHours?: string;

  // Location
  location?: [number, number];

  // Preferences
  notifications: boolean;

  // Verification and Security
  isEmailVerified: boolean;
  lastLogin?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface SignupRequest {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  username: string;
  dateOfBirth: string;
  gender: string;
  address: string;
  city: string;
  country: string;
  role: 'customer' | 'supplier';
  supplierId?: string;
  storeName?: string;
  businessType?: string;
  openHours?: string;
  location?: [number, number]; // [longitude, latitude]
  notifications?: boolean;
}

export interface AuthResponse {
  user: any;
  accessToken: string;
  refreshToken: string;
}

// Service interfaces
export interface Service {
  _id: string;
  key: string;
  label: string;
  icon: string;
  color: string;
  route: string;
  isActive: boolean;
  order: number;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  _id: string;
  key: string;
  label: string;
  icon: string;
  color: string;
  image?: string;
  bgGradient?: string;
  shadowColor?: string;
  subtitle?: string;
  badge?: string;
  badgeColor?: string;
  route: {
    pathname: string;
    params: {
      category: string;
    };
  };
  isActive: boolean;
  order: number;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Product {
  id: string;
  name: string;
  image: string;
  price: number;
  discountPrice?: number;
  category: string;
  description?: string;
  isAvailable: boolean;
  tags?: string[];
  nutritionInfo?: any;
  allergens?: string[];
  preparationTime?: string;
  customizations?: any[];
  restaurantOptions?: {
    additions?: Array<{ id: string; name: string; price: number }>;
    without?: string[];
    sides?: Array<{ id: string; name: string; price: number }>;
  };
  clothingOptions?: {
    sizes: string[];
    colors: string[];
    gallery: string[];
  };
}

export interface Supplier {
  _id: string;
  id: string;
  name: string;
  lat: number;
  lng: number;
  category: string;
  rating: number;
  tags: string[];
  logoUrl: string;
  banner: string;
  openHours: string;
  deliveryTime: string;
  phone: string;
  address?: string;
  description?: string;
  isActive: boolean;
  products: Product[];
  createdAt: string;
  updatedAt: string;
  ratings: number;
}

// Search result types
export interface SearchResult {
  type: 'service' | 'category' | 'supplier' | 'product';
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  icon?: string;
  color?: string;
  route: string;
  data: any; // Original data object
}

export interface ComprehensiveSearchResults {
  services: SearchResult[];
  categories: SearchResult[];
  suppliers: SearchResult[];
  products: SearchResult[];
  total: number;
}

// Order related interfaces
export interface OrderItem {
  productId: string;
  productName: string;
  quantity: number;
  price: number;
  subtotal: number;
  options?: {
    selectedAdditions?: Array<{ id: string; name: string; price: number }>;
    selectedSides?: Array<{ id: string; name: string; price: number }>;
    without?: string[];
    selectedSize?: string;
    selectedColor?: string;
    customSelections?: Record<string, any>;
  };
}

export interface DeliveryAddress {
  street: string;
  city: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  notes?: string;
}

export interface CreateOrderRequest {
  supplierId: string;
  supplierName: string;
  items: OrderItem[];
  subtotal: number;
  deliveryFee: number;
  totalAmount: number;
  paymentMethod: 'cash' | 'card' | 'wallet';
  deliveryAddress: DeliveryAddress;
  customerPhone: string;
  notes?: string;
  promoCode?: string;
}

export interface Order {
  orderId: string;
  customerId: string;
  supplierId: string;
  supplierName: string;
  items: OrderItem[];
  subtotal: number;
  deliveryFee: number;
  totalAmount: number;
  paymentMethod: 'cash' | 'card' | 'wallet';
  deliveryAddress: DeliveryAddress;
  customerPhone: string;
  notes?: string;
  promoCode?: string;
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'picked_up' | 'on_the_way' | 'delivered' | 'cancelled';
  estimatedDeliveryTime?: string;
  driverName?: string;
  driverPhone?: string;
  driverLocation?: {
    lat: number;
    lng: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface PromoCodeValidation {
  isValid: boolean;
  discountAmount?: number;
  discountPercentage?: number;
  message?: string;
}

class ApiService {
  private baseURL: string;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';
    this.loadTokensFromStorage();
  }

  private async loadTokensFromStorage(): Promise<void> {
    try {
      this.accessToken = localStorage.getItem('accessToken');
      this.refreshToken = localStorage.getItem('refreshToken');
    } catch (error) {
      console.error('Failed to load tokens from storage:', error);
    }
  }

  private async saveTokensToStorage(accessToken: string, refreshToken: string): Promise<void> {
    try {
      this.accessToken = accessToken;
      this.refreshToken = refreshToken;
      localStorage.setItem('accessToken', accessToken);
      localStorage.setItem('refreshToken', refreshToken);
    } catch (error) {
      console.error('Failed to save tokens to storage:', error);
    }
  }

  private async clearTokensFromStorage(): Promise<void> {
    try {
      this.accessToken = null;
      this.refreshToken = null;
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
    } catch (error) {
      console.error('Failed to clear tokens from storage:', error);
    }
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Merge existing headers if they exist
      if (options.headers) {
        if (options.headers instanceof Headers) {
          options.headers.forEach((value, key) => {
            headers[key] = value;
          });
        } else if (Array.isArray(options.headers)) {
          options.headers.forEach(([key, value]) => {
            headers[key] = value;
          });
        } else {
          Object.assign(headers, options.headers);
        }
      }

      if (this.accessToken) {
        headers.Authorization = `Bearer ${this.accessToken}`;
      }

      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          message: data.message || 'Request failed',
          error: data.error,
        };
      }

      return {
        success: true,
        data: data.data || data,
        message: data.message,
      };
    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        message: 'Network error occurred',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  // Authentication methods
  async login(credentials: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await this.makeRequest<AuthResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    if (response.success && response.data && response.data.accessToken && response.data.refreshToken) {
      await this.saveTokensToStorage(response.data.accessToken, response.data.refreshToken);
    }

    return response;
  }

  async signup(userData: SignupRequest): Promise<ApiResponse<any>> {
    const response = await this.makeRequest<any>('/auth/signup', {
      method: 'POST',
      body: JSON.stringify(userData),
    });

    return response;
  }

  async logout(): Promise<ApiResponse> {
    const response = await this.makeRequest('/auth/logout', {
      method: 'POST',
      body: JSON.stringify({ refreshToken: this.refreshToken }),
    });

    await this.clearTokensFromStorage();
    return response;
  }

  async forgotPassword(email: string): Promise<ApiResponse> {
    return this.makeRequest('/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  async verifyResetCode(email: string, code: string): Promise<ApiResponse> {
    return this.makeRequest('/auth/verify-reset-code', {
      method: 'POST',
      body: JSON.stringify({ email, code }),
    });
  }

  async resetPassword(data: { email: string; code: string; newPassword: string }): Promise<ApiResponse> {
    return this.makeRequest('/auth/reset-password', {
      method: 'POST',
      body: JSON.stringify({
        email: data.email,
        code: data.code,
        password: data.newPassword
      }),
    });
  }

  async verifyEmail(data: { token: string }): Promise<ApiResponse<AuthResponse>> {
    const response = await this.makeRequest<AuthResponse>('/auth/verify-email', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.success && response.data) {
      await this.saveTokensToStorage(response.data.accessToken, response.data.refreshToken);
    }

    return response;
  }

  async resendVerificationEmail(data: { email: string }): Promise<ApiResponse<void>> {
    return await this.makeRequest<void>('/auth/resend-verification', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getProfile(): Promise<ApiResponse<{ user: User }>> {
    return this.makeRequest<{ user: User }>('/users/profile');
  }

  // Services API
  async getServices(): Promise<ApiResponse<Service[]>> {
    return this.makeRequest<Service[]>('/services');
  }

  // Categories API
  async getCategories(): Promise<ApiResponse<Category[]>> {
    return this.makeRequest<Category[]>('/categories');
  }

  async getCategoryByKey(key: string): Promise<ApiResponse<Category>> {
    return this.makeRequest<Category>(`/categories/${key}`);
  }

  // Suppliers API
  async getSuppliers(params?: {
    category?: string;
    search?: string;
    lat?: number;
    lng?: number;
    radius?: number;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<ApiResponse<Supplier[]>> {
    const queryParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/suppliers${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.makeRequest<Supplier[]>(endpoint);
  }

  async getSuppliersByCategory(
    category: string,
    params?: {
      search?: string;
      page?: number;
      limit?: number;
    }
  ): Promise<ApiResponse<Supplier[]>> {
    const queryParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/suppliers/category/${category}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.makeRequest<Supplier[]>(endpoint);
  }

  async getSupplierById(id: string): Promise<ApiResponse<Supplier>> {
    return this.makeRequest<Supplier>(`/suppliers/${id}`);
  }

  async getSupplierProducts(
    id: string,
    params?: {
      category?: string;
      search?: string;
      page?: number;
      limit?: number;
    }
  ): Promise<ApiResponse<{
    supplier: {
      id: string;
      name: string;
      rating: number;
      deliveryTime: string;
      openHours: string;
    };
    products: Product[];
    categories: string[];
    pagination?: any;
  }>> {
    const queryParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/suppliers/${id}/products${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.makeRequest<{
      supplier: any;
      products: Product[];
      categories: string[];
      pagination?: any;
    }>(endpoint);
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  getAccessToken(): string | null {
    return this.accessToken;
  }

  // Orders API
  async createOrder(orderData: CreateOrderRequest): Promise<ApiResponse<Order>> {
    return this.makeRequest<Order>('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
  }

  async getUserOrders(params?: {
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<ApiResponse<{ orders: Order[]; pagination?: any }>> {
    const queryParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/orders${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.makeRequest<{ orders: Order[]; pagination?: any }>(endpoint);
  }

  async getOrderById(orderId: string): Promise<ApiResponse<Order>> {
    return this.makeRequest<Order>(`/orders/${orderId}`);
  }

  async updateOrderStatus(
    orderId: string,
    status: string,
    trackingUpdate?: any
  ): Promise<ApiResponse<Order>> {
    return this.makeRequest<Order>(`/orders/${orderId}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status, trackingUpdate }),
    });
  }

  // Promo Code API
  async validatePromoCode(code: string, orderTotal: number): Promise<ApiResponse<PromoCodeValidation>> {
    return this.makeRequest<PromoCodeValidation>('/promo-codes/validate', {
      method: 'POST',
      body: JSON.stringify({ code, orderTotal }),
    });
  }

  // Delivery Fee API
  async calculateDeliveryFee(params: {
    supplierId: string; // Required - each supplier has separate delivery fee
    deliveryAddress: {
      lat: number;
      lng: number;
    };
    orderTotal?: number;
  }): Promise<ApiResponse<{ deliveryFee: number; estimatedTime?: string }>> {
    return this.makeRequest<{ deliveryFee: number; estimatedTime?: string }>('/delivery/calculate-fee', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  }

  // Calculate delivery fees for multiple suppliers
  async calculateMultipleDeliveryFees(params: {
    supplierIds: string[];
    deliveryAddress: {
      lat: number;
      lng: number;
    };
    orderTotals?: Record<string, number>;
  }): Promise<ApiResponse<Record<string, { deliveryFee: number; estimatedTime?: string }>>> {
    return this.makeRequest<Record<string, { deliveryFee: number; estimatedTime?: string }>>('/delivery/calculate-multiple-fees', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  }
}

export const apiService = new ApiService();

// Helper functions for search
const containsArabic = (text: string): boolean => {
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  return arabicRegex.test(text);
};

const matchesSearchTerm = (text: string, searchTerm: string, _translationKey?: string): boolean => {
  if (!text) return false;

  const normalizedText = text.toLowerCase().trim();
  const normalizedSearch = searchTerm.toLowerCase().trim();

  // Direct match
  if (normalizedText.includes(normalizedSearch)) {
    return true;
  }

  // Word boundary match
  const words = normalizedText.split(/\s+/);
  return words.some(word => word.startsWith(normalizedSearch));
};

const getTranslatedText = (_key: string, fallback: string, _language: 'en' | 'ar'): string => {
  // For now, return the fallback. In a real app, this would use i18n
  return fallback;
};

// Comprehensive search function
export const comprehensiveSearch = async (query: string): Promise<ComprehensiveSearchResults> => {
  if (!query.trim()) {
    return {
      services: [],
      categories: [],
      suppliers: [],
      products: [],
      total: 0
    };
  }

  const searchTerm = query.toLowerCase().trim();

  // Detect search language based on the query
  const isArabicSearch = containsArabic(query);
  const preferredLanguage: 'en' | 'ar' = isArabicSearch ? 'ar' : 'en';

  const results: ComprehensiveSearchResults = {
    services: [],
    categories: [],
    suppliers: [],
    products: [],
    total: 0
  };

  try {
    // Search services
    const servicesResponse = await apiService.getServices();
    if (servicesResponse.success && servicesResponse.data) {
      results.services = servicesResponse.data
        .filter(service =>
          matchesSearchTerm(service.label, searchTerm, `services.${service.key}`) ||
          (service.description && matchesSearchTerm(service.description, searchTerm))
        )
        .map(service => ({
          type: 'service' as const,
          id: service._id,
          title: getTranslatedText(`services.${service.key}`, service.label, preferredLanguage),
          description: service.description,
          icon: service.icon,
          color: service.color,
          route: service.route,
          data: service
        }));
    }

    // Search categories
    const categoriesResponse = await apiService.getCategories();
    if (categoriesResponse.success && categoriesResponse.data) {
      results.categories = categoriesResponse.data
        .filter(category =>
          matchesSearchTerm(category.label, searchTerm, `categories.${category.key}`) ||
          (category.description && matchesSearchTerm(category.description, searchTerm))
        )
        .map(category => ({
          type: 'category' as const,
          id: category._id,
          title: getTranslatedText(`categories.${category.key}`, category.label, preferredLanguage),
          description: category.description,
          icon: category.icon,
          color: category.color,
          route: `/customer/suppliers-page?category=${category.key}`,
          data: category
        }));
    }

    // Search suppliers - use backend search for suppliers as it's more efficient
    const suppliersResponse = await apiService.getSuppliers({ search: searchTerm });
    if (suppliersResponse.success && suppliersResponse.data) {
      // Also search all suppliers locally for better bilingual support
      const allSuppliersResponse = await apiService.getSuppliers();
      let combinedSuppliers = suppliersResponse.data || [];

      if (allSuppliersResponse.success && allSuppliersResponse.data) {
        const localFilteredSuppliers = allSuppliersResponse.data.filter(supplier =>
          matchesSearchTerm(supplier.name, searchTerm) ||
          matchesSearchTerm(supplier.category, searchTerm, `categories.${supplier.category}`) ||
          (supplier.description && matchesSearchTerm(supplier.description, searchTerm)) ||
          (supplier.tags && supplier.tags.some(tag => matchesSearchTerm(tag, searchTerm)))
        );

        // Combine and deduplicate results
        localFilteredSuppliers.forEach(supplier => {
          if (!combinedSuppliers.find(s => s.id === supplier.id)) {
            combinedSuppliers.push(supplier);
          }
        });
      }

      results.suppliers = combinedSuppliers.map(supplier => ({
        type: 'supplier' as const,
        id: supplier.id,
        title: supplier.name,
        subtitle: getTranslatedText(`categories.${supplier.category}`, supplier.category, preferredLanguage),
        description: supplier.description,
        route: `/customer/supplier-details?supplierId=${supplier.id}`,
        data: supplier
      }));
    }

    // Search products across all suppliers with bilingual support
    const productResults: SearchResult[] = [];
    if (suppliersResponse.success && suppliersResponse.data) {
      for (const supplier of suppliersResponse.data) {
        if (supplier.products && supplier.products.length > 0) {
          const matchingProducts = supplier.products.filter(product =>
            matchesSearchTerm(product.name, searchTerm) ||
            matchesSearchTerm(product.category, searchTerm, `categories.${product.category}`) ||
            (product.description && matchesSearchTerm(product.description, searchTerm)) ||
            (product.tags && product.tags.some(tag => matchesSearchTerm(tag, searchTerm)))
          );

          matchingProducts.forEach(product => {
            productResults.push({
              type: 'product' as const,
              id: product.id,
              title: product.name,
              subtitle: `${supplier.name} • ${getTranslatedText(`categories.${product.category}`, product.category, preferredLanguage)}`,
              description: product.description,
              route: `/customer/supplier-product-details?productId=${product.id}&supplierId=${supplier.id}&supplierName=${supplier.name}&category=${supplier.category}&product=${encodeURIComponent(JSON.stringify(product))}`,
              data: { ...product, supplier }
            });
          });
        }
      }
    }

    results.products = productResults;
    results.total = results.services.length + results.categories.length + results.suppliers.length + results.products.length;

  } catch (error) {
    console.error('Error in comprehensive search:', error);
  }

  return results;
};
